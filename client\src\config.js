// Configuration file for environment variables

// API URLs
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';
export const API_URL = API_BASE_URL; // For backward compatibility
export const AUTH_API_URL = `${API_BASE_URL}/auth`;
export const FILES_API_URL = `${API_BASE_URL}/files`;
export const USERS_API_URL = `${API_BASE_URL}/users`;

// Other configuration
export const APP_NAME = 'Secure File Transfer';
export const STORAGE_LIMIT = 1024 * 1024 * 1024; // 1GB

// Default export for backward compatibility
const config = {
  API_URL: API_BASE_URL,
  API_BASE_URL,
  AUTH_API_URL,
  FILES_API_URL,
  USERS_API_URL,
  APP_NAME,
  STORAGE_LIMIT
};

export default config;
