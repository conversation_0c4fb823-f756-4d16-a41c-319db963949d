import { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Table,
  Button,
  Space,
  Popconfirm,
  Card,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Tooltip,
  Divider,
  Row,
  Col,
  Statistic,
  App,
  Switch
} from 'antd';
import {
  KeyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SafetyOutlined,
  FileOutlined,
  SettingOutlined,
  EyeOutlined,
  TeamOutlined
} from '@ant-design/icons';
import DashboardLayout from '../../components/Sidebar';
import {
  getPermissions,
  createPermission,
  updatePermission,
  deletePermission,
  togglePermissionStatus,
  getPermissionStats
} from '../../api/permissionService';

const { Title, Text } = Typography;
const { Option } = Select;

const Permissions = () => {
  const { message } = App.useApp();
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create' or 'edit'
  const [selectedPermission, setSelectedPermission] = useState(null);
  const [form] = Form.useForm();

  const categories = ['Administration', 'File Operations', 'Analytics', 'Security'];

  // Load permissions from API
  const loadPermissions = async () => {
    try {
      setLoading(true);
      const response = await getPermissions();
      setPermissions(response.data || []);
    } catch (error) {
      console.error('Error loading permissions:', error);
      message.error('Failed to load permissions');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPermissions();
  }, []);

  const showCreateModal = () => {
    setModalType('create');
    setSelectedPermission(null);
    form.resetFields();
    form.setFieldsValue({ isActive: true });
    setModalVisible(true);
  };

  const showEditModal = (permission) => {
    setModalType('edit');
    setSelectedPermission(permission);
    form.setFieldsValue({
      name: permission.name,
      code: permission.code,
      description: permission.description,
      category: permission.category,
      isActive: permission.isActive
    });
    setModalVisible(true);
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (modalType === 'create') {
        await createPermission(values);
        message.success('Permission created successfully');
      } else {
        await updatePermission(selectedPermission._id, values);
        message.success('Permission updated successfully');
      }

      setModalVisible(false);
      loadPermissions(); // Refresh permissions list
    } catch (error) {
      console.error('Form submission error:', error);
      message.error(error.message || 'Operation failed');
    }
  };

  const handleDelete = async (id) => {
    try {
      await deletePermission(id);
      message.success('Permission deleted successfully');
      loadPermissions(); // Refresh permissions list
    } catch (error) {
      console.error('Error deleting permission:', error);
      message.error('Failed to delete permission');
    }
  };

  const handleToggleStatus = async (permission) => {
    try {
      await togglePermissionStatus(permission._id);
      message.success(`Permission ${permission.isActive ? 'disabled' : 'enabled'} successfully`);
      loadPermissions(); // Refresh permissions list
    } catch (error) {
      console.error('Error toggling permission status:', error);
      message.error('Failed to update permission status');
    }
  };

  const getPermissionStats = () => {
    const totalPermissions = permissions.length;
    const activePermissions = permissions.filter(p => p.isActive).length;
    const systemPermissions = permissions.filter(p => p.isSystem).length;
    const customPermissions = totalPermissions - systemPermissions;

    return {
      totalPermissions,
      activePermissions,
      systemPermissions,
      customPermissions
    };
  };

  const stats = getPermissionStats();

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'Administration':
        return <SettingOutlined />;
      case 'File Operations':
        return <FileOutlined />;
      case 'Analytics':
        return <EyeOutlined />;
      case 'Security':
        return <SafetyOutlined />;
      default:
        return <KeyOutlined />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'Administration':
        return '#ff4d4f';
      case 'File Operations':
        return '#1890ff';
      case 'Analytics':
        return '#52c41a';
      case 'Security':
        return '#faad14';
      default:
        return '#8c8c8c';
    }
  };

  const columns = [
    {
      title: 'Permission',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <KeyOutlined
            style={{
              fontSize: '20px',
              marginRight: '12px',
              color: record.isActive ? '#00BF96' : '#8c8c8c'
            }}
          />
          <div>
            <div style={{ fontWeight: '500', display: 'flex', alignItems: 'center' }}>
              {text}
              {record.isSystem && (
                <Tag color="green" size="small" style={{ marginLeft: '8px' }}>
                  System
                </Tag>
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              Code: {record.code}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      width: '25%',
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: '15%',
      render: (category) => (
        <Tag
          icon={getCategoryIcon(category)}
          color={getCategoryColor(category)}
          style={{ borderRadius: '12px', padding: '4px 8px' }}
        >
          {category}
        </Tag>
      ),
    },
    {
      title: 'Roles',
      dataIndex: 'roleCount',
      key: 'roleCount',
      width: '10%',
      render: (count) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <TeamOutlined style={{ color: '#8c8c8c', marginRight: '6px' }} />
          <span>{count}</span>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'isActive',
      key: 'isActive',
      width: '10%',
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleStatus(record)}
          disabled={record.isSystem}
          checkedChildren="Active"
          unCheckedChildren="Inactive"
        />
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '15%',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Permission">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
              shape="circle"
              className="gradient-button"
            />
          </Tooltip>
          {!record.isSystem && (
            <Tooltip title="Delete Permission">
              <Popconfirm
                title="Delete this permission?"
                description="This action cannot be undone. Roles with this permission will lose access."
                onConfirm={() => handleDelete(record._id)}
                okText="Yes"
                cancelText="No"
                okButtonProps={{ danger: true }}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  shape="circle"
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <DashboardLayout>
      <div className="fade-in">
      <div style={{ marginBottom: '24px' }}>
        <Title level={3} style={{ margin: '0 0 8px 0' }}>Permission Management</Title>
        <Text type="secondary">Manage system permissions and access controls</Text>
      </div>

      

      <Card
        className="dashboard-card"
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <KeyOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
            <span>All Permissions</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
            className="gradient-button"
          >
            Create Permission
          </Button>
        }
      >
        <Table
          className="custom-table"
          columns={columns}
          dataSource={permissions}
          rowKey="_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showTotal: (total) => `Total ${total} permissions`,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </Card>

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {modalType === 'create' ? (
              <>
                <PlusOutlined style={{ fontSize: '20px', marginRight: '8px', color: '#00BF96' }} />
                Create New Permission
              </>
            ) : (
              <>
                <EditOutlined style={{ fontSize: '20px', marginRight: '8px', color: '#00BF96' }} />
                Edit Permission
              </>
            )}
          </div>
        }
        open={modalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalSubmit}
        okText={modalType === 'create' ? 'Create' : 'Update'}
        okButtonProps={{
          className: 'gradient-button',
          style: { borderColor: 'transparent' }
        }}
        width={600}
        centered
      >
        <Divider style={{ margin: '0 0 24px 0' }} />

        <Form
          form={form}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="name"
            label="Permission Name"
            rules={[{ required: true, message: 'Please enter permission name' }]}
          >
            <Input
              prefix={<KeyOutlined style={{ color: '#00BF96' }} />}
              placeholder="Enter permission name"
            />
          </Form.Item>

          <Form.Item
            name="code"
            label="Permission Code"
            rules={[
              { required: true, message: 'Please enter permission code' },
              { pattern: /^[a-z_]+$/, message: 'Code must contain only lowercase letters and underscores' }
            ]}
            extra="Use lowercase letters and underscores only (e.g., user_management)"
          >
            <Input
              placeholder="Enter permission code"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <Input.TextArea
              placeholder="Enter permission description"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="category"
            label="Category"
            rules={[{ required: true, message: 'Please select category' }]}
          >
            <Select placeholder="Select category">
              {categories.map(category => (
                <Option key={category} value={category}>
                  <Space>
                    {getCategoryIcon(category)}
                    {category}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="Status"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="Active"
              unCheckedChildren="Inactive"
            />
          </Form.Item>
        </Form>
      </Modal>
      </div>
    </DashboardLayout>
  );
};

export default Permissions;
