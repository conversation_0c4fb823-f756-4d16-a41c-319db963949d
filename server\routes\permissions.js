const express = require('express');
const {
  getPermissions,
  getPermission,
  createPermission,
  updatePermission,
  deletePermission,
  togglePermissionStatus,
  getPermissionStats
} = require('../controllers/permissions');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply protection to all routes
router.use(protect);
// Apply admin authorization to all routes
router.use(authorize('admin'));

// Permission statistics route (must be before /:id route)
router.get('/stats', getPermissionStats);

router.route('/')
  .get(getPermissions)
  .post(createPermission);

router.route('/:id')
  .get(getPermission)
  .put(updatePermission)
  .delete(deletePermission);

// Toggle permission status
router.patch('/:id/toggle-status', togglePermissionStatus);

module.exports = router;
