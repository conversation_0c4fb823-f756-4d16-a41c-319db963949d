const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import User model
const User = require('../models/User');

// MongoDB connection
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/secure-file-transfer';

async function fixUserRoles() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('Connected to MongoDB');

    // Find all users with invalid roles
    const users = await User.find({
      role: { $nin: ['user', 'admin'] }
    });

    console.log(`Found ${users.length} users with invalid roles`);

    if (users.length === 0) {
      console.log('No users with invalid roles found. Database is clean.');
      return;
    }

    // Fix each user's role
    for (const user of users) {
      console.log(`Fixing user ${user.email}: role "${user.role}" -> "user"`);
      
      // Update the role to 'user' (default)
      await User.findByIdAndUpdate(
        user._id,
        { role: 'user' },
        { runValidators: true }
      );
    }

    console.log(`Successfully fixed ${users.length} user roles`);

    // Verify the fix
    const remainingInvalidUsers = await User.find({
      role: { $nin: ['user', 'admin'] }
    });

    if (remainingInvalidUsers.length === 0) {
      console.log('✅ All user roles have been fixed successfully!');
    } else {
      console.log(`❌ ${remainingInvalidUsers.length} users still have invalid roles`);
    }

  } catch (error) {
    console.error('Error fixing user roles:', error);
  } finally {
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the fix
fixUserRoles();
