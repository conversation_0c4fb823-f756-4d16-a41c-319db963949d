const mongoose = require('mongoose');
const dotenv = require('dotenv');
const Role = require('../models/Role');
const Permission = require('../models/Permission');

// Load environment variables
dotenv.config();

// MongoDB connection
const MONGO_URI = process.env.MONGO_URI || 'mongodb://localhost:27017/secure-file-transfer';

async function seedRolesAndPermissions() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGO_URI);
    console.log('Connected to MongoDB');

    // Clear existing roles and permissions
    await Role.deleteMany({});
    await Permission.deleteMany({});
    console.log('Cleared existing roles and permissions');

    // Create permissions
    const permissions = [
      {
        name: 'User Management',
        code: 'user_management',
        description: 'Create, edit, and delete users',
        category: 'Administration',
        isSystem: true
      },
      {
        name: 'File Management',
        code: 'file_management',
        description: 'Manage all files in the system',
        category: 'File Operations',
        isSystem: true
      },
      {
        name: 'System Settings',
        code: 'system_settings',
        description: 'Configure system settings',
        category: 'Administration',
        isSystem: true
      },
      {
        name: 'View Analytics',
        code: 'view_analytics',
        description: 'Access system analytics and reports',
        category: 'Analytics',
        isSystem: true
      },
      {
        name: 'File Upload',
        code: 'file_upload',
        description: 'Upload files to the system',
        category: 'File Operations',
        isSystem: true
      },
      {
        name: 'File Download',
        code: 'file_download',
        description: 'Download files from the system',
        category: 'File Operations',
        isSystem: true
      },
      {
        name: 'View Own Files',
        code: 'view_own_files',
        description: 'View own uploaded files',
        category: 'File Operations',
        isSystem: true
      },
      {
        name: 'View All Files',
        code: 'view_all_files',
        description: 'View all files in the system',
        category: 'File Operations',
        isSystem: true
      },
      {
        name: 'Role Management',
        code: 'role_management',
        description: 'Create, edit, and delete roles',
        category: 'Administration',
        isSystem: true
      },
      {
        name: 'Permission Management',
        code: 'permission_management',
        description: 'Create, edit, and delete permissions',
        category: 'Administration',
        isSystem: true
      }
    ];

    const createdPermissions = await Permission.insertMany(permissions);
    console.log(`Created ${createdPermissions.length} permissions`);

    // Create permission map for easy reference
    const permissionMap = {};
    createdPermissions.forEach(permission => {
      permissionMap[permission.code] = permission._id;
    });

    // Create roles
    const roles = [
      {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        permissions: Object.values(permissionMap), // All permissions
        isSystem: true
      },
      {
        name: 'User',
        description: 'Basic user access for file operations',
        permissions: [
          permissionMap.file_upload,
          permissionMap.file_download,
          permissionMap.view_own_files
        ],
        isSystem: true
      },
      {
        name: 'File Manager',
        description: 'Can manage files and view user activities',
        permissions: [
          permissionMap.file_management,
          permissionMap.view_all_files,
          permissionMap.view_analytics,
          permissionMap.file_upload,
          permissionMap.file_download
        ],
        isSystem: false
      },
      {
        name: 'Moderator',
        description: 'Can manage users and view analytics',
        permissions: [
          permissionMap.user_management,
          permissionMap.view_analytics,
          permissionMap.view_all_files,
          permissionMap.file_upload,
          permissionMap.file_download,
          permissionMap.view_own_files
        ],
        isSystem: false
      }
    ];

    const createdRoles = await Role.insertMany(roles);
    console.log(`Created ${createdRoles.length} roles`);

    console.log('\nSeeding completed successfully!');
    console.log('\nCreated Permissions:');
    createdPermissions.forEach(permission => {
      console.log(`- ${permission.name} (${permission.code}) - ${permission.category}`);
    });

    console.log('\nCreated Roles:');
    createdRoles.forEach(role => {
      console.log(`- ${role.name}: ${role.permissions.length} permissions`);
    });

    process.exit(0);
  } catch (error) {
    console.error('Error seeding roles and permissions:', error);
    process.exit(1);
  }
}

// Run the seeding function
seedRolesAndPermissions();
