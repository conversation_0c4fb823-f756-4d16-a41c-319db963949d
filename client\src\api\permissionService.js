import axios from 'axios';
import config from '../config';

const API_URL = `${config.API_URL}/permissions`;

// Get auth token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Set up axios interceptor for auth
const axiosInstance = axios.create();
axiosInstance.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Get all permissions
export const getPermissions = async () => {
  try {
    const response = await axiosInstance.get(API_URL);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch permissions');
  }
};

// Get single permission
export const getPermission = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch permission');
  }
};

// Create new permission
export const createPermission = async (permissionData) => {
  try {
    const response = await axiosInstance.post(API_URL, permissionData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to create permission');
  }
};

// Update permission
export const updatePermission = async (id, permissionData) => {
  try {
    const response = await axiosInstance.put(`${API_URL}/${id}`, permissionData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to update permission');
  }
};

// Delete permission
export const deletePermission = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to delete permission');
  }
};

// Toggle permission status
export const togglePermissionStatus = async (id) => {
  try {
    const response = await axiosInstance.patch(`${API_URL}/${id}/toggle-status`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to toggle permission status');
  }
};

// Get permission statistics
export const getPermissionStats = async () => {
  try {
    const response = await axiosInstance.get(`${API_URL}/stats`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch permission statistics');
  }
};
