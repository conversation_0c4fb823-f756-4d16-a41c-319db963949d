const Role = require('../models/Role');
const Permission = require('../models/Permission');
const User = require('../models/User');

// @desc    Get all roles
// @route   GET /api/roles
// @access  Private/Admin
exports.getRoles = async (req, res) => {
  try {
    const roles = await Role.find()
      .populate('permissions', 'name code')
      .populate('userCount')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: roles.length,
      data: roles
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single role
// @route   GET /api/roles/:id
// @access  Private/Admin
exports.getRole = async (req, res) => {
  try {
    const role = await Role.findById(req.params.id)
      .populate('permissions', 'name code description category')
      .populate('userCount');

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    res.status(200).json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create role
// @route   POST /api/roles
// @access  Private/Admin
exports.createRole = async (req, res) => {
  try {
    const { name, description, permissions } = req.body;

    // Check if role already exists
    const existingRole = await Role.findOne({ name });
    if (existingRole) {
      return res.status(400).json({
        success: false,
        message: 'Role with this name already exists'
      });
    }

    // Validate permissions exist
    if (permissions && permissions.length > 0) {
      const validPermissions = await Permission.find({ _id: { $in: permissions } });
      if (validPermissions.length !== permissions.length) {
        return res.status(400).json({
          success: false,
          message: 'One or more permissions are invalid'
        });
      }
    }

    // Create role
    const role = await Role.create({
      name,
      description,
      permissions: permissions || []
    });

    // Populate the created role
    await role.populate('permissions', 'name code');

    res.status(201).json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update role
// @route   PUT /api/roles/:id
// @access  Private/Admin
exports.updateRole = async (req, res) => {
  try {
    const { name, description, permissions } = req.body;

    let role = await Role.findById(req.params.id);

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Check if it's a system role and prevent modification of critical fields
    if (role.isSystem && (name !== role.name)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot modify system role name'
      });
    }

    // Check if new name already exists (if name is being changed)
    if (name && name !== role.name) {
      const existingRole = await Role.findOne({ name });
      if (existingRole) {
        return res.status(400).json({
          success: false,
          message: 'Role with this name already exists'
        });
      }
    }

    // Validate permissions exist
    if (permissions && permissions.length > 0) {
      const validPermissions = await Permission.find({ _id: { $in: permissions } });
      if (validPermissions.length !== permissions.length) {
        return res.status(400).json({
          success: false,
          message: 'One or more permissions are invalid'
        });
      }
    }

    // Update role
    role = await Role.findByIdAndUpdate(
      req.params.id,
      {
        name: name || role.name,
        description: description || role.description,
        permissions: permissions || role.permissions
      },
      {
        new: true,
        runValidators: true
      }
    ).populate('permissions', 'name code');

    res.status(200).json({
      success: true,
      data: role
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete role
// @route   DELETE /api/roles/:id
// @access  Private/Admin
exports.deleteRole = async (req, res) => {
  try {
    const role = await Role.findById(req.params.id);

    if (!role) {
      return res.status(404).json({
        success: false,
        message: 'Role not found'
      });
    }

    // Prevent deletion of system roles
    if (role.isSystem) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete system role'
      });
    }

    // Check if any users have this role
    const usersWithRole = await User.countDocuments({ role: req.params.id });
    if (usersWithRole > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete role. ${usersWithRole} user(s) are assigned to this role`
      });
    }

    await Role.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get role statistics
// @route   GET /api/roles/stats
// @access  Private/Admin
exports.getRoleStats = async (req, res) => {
  try {
    const totalRoles = await Role.countDocuments();
    const systemRoles = await Role.countDocuments({ isSystem: true });
    const customRoles = await Role.countDocuments({ isSystem: false });
    const activeRoles = await Role.countDocuments({ isActive: true });

    // Get user count per role
    const roleUserCounts = await User.aggregate([
      {
        $group: {
          _id: '$role',
          userCount: { $sum: 1 }
        }
      }
    ]);

    const totalUsers = roleUserCounts.reduce((sum, item) => sum + item.userCount, 0);

    res.status(200).json({
      success: true,
      data: {
        totalRoles,
        systemRoles,
        customRoles,
        activeRoles,
        totalUsers,
        roleUserCounts
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
