{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "mongoose": "^8.14.2", "multer": "^1.4.5-lts.2", "nodemailer": "^6.10.1"}, "devDependencies": {"nodemon": "^3.1.10"}}