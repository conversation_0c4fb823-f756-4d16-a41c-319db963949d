import { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Table,
  Button,
  Space,
  Popconfirm,
  Card,
  Modal,
  Form,
  Input,
  Select,
  Tag,
  Tooltip,
  Divider,
  Row,
  Col,
  Statistic,
  App,
  Checkbox
} from 'antd';
import {
  SafetyOutlined,
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  KeyOutlined,
  UserOutlined,
  TeamOutlined,
  SettingOutlined
} from '@ant-design/icons';
import DashboardLayout from '../../components/Sidebar';
import {
  getRoles,
  createRole,
  updateRole,
  deleteRole,
  getRoleStats
} from '../../api/roleService';
import {
  getPermissions
} from '../../api/permissionService';

const { Title, Text } = Typography;
const { Option } = Select;

const Roles = () => {
  const { message } = App.useApp();
  const [roles, setRoles] = useState([]);
  const [permissions, setPermissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState('create'); // 'create' or 'edit'
  const [selectedRole, setSelectedRole] = useState(null);
  const [form] = Form.useForm();

  // Load roles and permissions from API
  const loadRoles = async () => {
    try {
      setLoading(true);
      const response = await getRoles();
      setRoles(response.data || []);
    } catch (error) {
      console.error('Error loading roles:', error);
      message.error('Failed to load roles');
    } finally {
      setLoading(false);
    }
  };

  const loadPermissions = async () => {
    try {
      const response = await getPermissions();
      setPermissions(response.data || []);
    } catch (error) {
      console.error('Error loading permissions:', error);
      message.error('Failed to load permissions');
    }
  };

  useEffect(() => {
    loadRoles();
    loadPermissions();
  }, []);

  const showCreateModal = () => {
    setModalType('create');
    setSelectedRole(null);
    form.resetFields();
    setModalVisible(true);
  };

  const showEditModal = (role) => {
    setModalType('edit');
    setSelectedRole(role);
    // Extract permission IDs from the role's permissions array
    const permissionIds = role.permissions.map(permission =>
      typeof permission === 'object' ? permission._id : permission
    );
    form.setFieldsValue({
      name: role.name,
      description: role.description,
      permissions: permissionIds
    });
    setModalVisible(true);
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (modalType === 'create') {
        await createRole(values);
        message.success('Role created successfully');
      } else {
        await updateRole(selectedRole._id, values);
        message.success('Role updated successfully');
      }

      setModalVisible(false);
      loadRoles(); // Refresh roles list
    } catch (error) {
      console.error('Form submission error:', error);
      message.error(error.message || 'Operation failed');
    }
  };

  const handleDelete = async (id) => {
    try {
      await deleteRole(id);
      message.success('Role deleted successfully');
      loadRoles(); // Refresh roles list
    } catch (error) {
      console.error('Error deleting role:', error);
      message.error('Failed to delete role');
    }
  };

  const getRoleStats = () => {
    const totalRoles = roles.length;
    const systemRoles = roles.filter(role => role.isSystem).length;
    const customRoles = totalRoles - systemRoles;
    const totalUsers = roles.reduce((sum, role) => sum + role.userCount, 0);

    return {
      totalRoles,
      systemRoles,
      customRoles,
      totalUsers
    };
  };

  const stats = getRoleStats();

  const getPermissionName = (permissionId) => {
    // Handle both object and string permission IDs
    if (typeof permissionId === 'object' && permissionId.name) {
      return permissionId.name;
    }
    const permission = permissions.find(p => p._id === permissionId || p.code === permissionId);
    return permission ? permission.name : permissionId;
  };

  const columns = [
    {
      title: 'Role',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <SafetyOutlined
            style={{
              fontSize: '20px',
              marginRight: '12px',
              color: record.isSystem ? '#00BF96' : '#1890ff'
            }}
          />
          <div>
            <div style={{ fontWeight: '500', display: 'flex', alignItems: 'center' }}>
              {text}
              {record.isSystem && (
                <Tag color="green" size="small" style={{ marginLeft: '8px' }}>
                  System
                </Tag>
              )}
            </div>
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>{record.description}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Permissions',
      dataIndex: 'permissions',
      key: 'permissions',
      width: '30%',
      render: (permissions) => (
        <div>
          {permissions.slice(0, 3).map(permission => (
            <Tag key={permission} style={{ marginBottom: '4px' }}>
              {getPermissionName(permission)}
            </Tag>
          ))}
          {permissions.length > 3 && (
            <Tag color="default">+{permissions.length - 3} more</Tag>
          )}
        </div>
      ),
    },
    {
      title: 'Users',
      dataIndex: 'userCount',
      key: 'userCount',
      width: '10%',
      render: (count) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <UserOutlined style={{ color: '#8c8c8c', marginRight: '6px' }} />
          <span>{count}</span>
        </div>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: '15%',
      render: (date) => (
        <span>{new Date(date).toLocaleDateString()}</span>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '15%',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Role">
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
              shape="circle"
              className="gradient-button"
            />
          </Tooltip>
          {!record.isSystem && (
            <Tooltip title="Delete Role">
              <Popconfirm
                title="Delete this role?"
                description="This action cannot be undone. Users with this role will lose their permissions."
                onConfirm={() => handleDelete(record._id)}
                okText="Yes"
                cancelText="No"
                okButtonProps={{ danger: true }}
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                  shape="circle"
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <DashboardLayout>
      <div className="fade-in">
      <div style={{ marginBottom: '24px' }}>
        <Title level={3} style={{ margin: '0 0 8px 0' }}>Role Management</Title>
        <Text type="secondary">Manage user roles and their permissions</Text>
      </div>

      

      <Card
        className="dashboard-card"
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <SafetyOutlined style={{ marginRight: '8px', fontSize: '18px' }} />
            <span>All Roles</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
            className="gradient-button"
          >
            Create Role
          </Button>
        }
      >
        <Table
          className="custom-table"
          columns={columns}
          dataSource={roles}
          rowKey="_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showTotal: (total) => `Total ${total} roles`,
            showSizeChanger: true,
            pageSizeOptions: ['10', '20', '50']
          }}
        />
      </Card>

      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {modalType === 'create' ? (
              <>
                <PlusOutlined style={{ fontSize: '20px', marginRight: '8px', color: '#00BF96' }} />
                Create New Role
              </>
            ) : (
              <>
                <EditOutlined style={{ fontSize: '20px', marginRight: '8px', color: '#00BF96' }} />
                Edit Role
              </>
            )}
          </div>
        }
        open={modalVisible}
        onCancel={handleModalCancel}
        onOk={handleModalSubmit}
        okText={modalType === 'create' ? 'Create' : 'Update'}
        okButtonProps={{
          className: 'gradient-button',
          style: { borderColor: 'transparent' }
        }}
        width={600}
        centered
      >
        <Divider style={{ margin: '0 0 24px 0' }} />

        <Form
          form={form}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="name"
            label="Role Name"
            rules={[{ required: true, message: 'Please enter role name' }]}
          >
            <Input
              prefix={<SafetyOutlined style={{ color: '#00BF96' }} />}
              placeholder="Enter role name"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="Description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <Input.TextArea
              placeholder="Enter role description"
              rows={3}
            />
          </Form.Item>

          <Form.Item
            name="permissions"
            label="Permissions"
            rules={[{ required: true, message: 'Please select at least one permission' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 16]}>
                {permissions.map(permission => (
                  <Col span={12} key={permission._id}>
                    <Checkbox value={permission._id}>
                      <div>
                        <div style={{ fontWeight: '500' }}>{permission.name}</div>
                        <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
                          {permission.description}
                        </div>
                      </div>
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
      </div>
    </DashboardLayout>
  );
};

export default Roles;
