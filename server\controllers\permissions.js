const Permission = require('../models/Permission');
const Role = require('../models/Role');

// @desc    Get all permissions
// @route   GET /api/permissions
// @access  Private/Admin
exports.getPermissions = async (req, res) => {
  try {
    const permissions = await Permission.find()
      .populate('roleCount')
      .sort({ category: 1, name: 1 });

    res.status(200).json({
      success: true,
      count: permissions.length,
      data: permissions
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get single permission
// @route   GET /api/permissions/:id
// @access  Private/Admin
exports.getPermission = async (req, res) => {
  try {
    const permission = await Permission.findById(req.params.id)
      .populate('roleCount');

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    res.status(200).json({
      success: true,
      data: permission
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create permission
// @route   POST /api/permissions
// @access  Private/Admin
exports.createPermission = async (req, res) => {
  try {
    const { name, code, description, category, isActive } = req.body;

    // Check if permission code already exists
    const existingPermission = await Permission.findOne({ code });
    if (existingPermission) {
      return res.status(400).json({
        success: false,
        message: 'Permission with this code already exists'
      });
    }

    // Create permission
    const permission = await Permission.create({
      name,
      code,
      description,
      category,
      isActive: isActive !== undefined ? isActive : true
    });

    res.status(201).json({
      success: true,
      data: permission
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update permission
// @route   PUT /api/permissions/:id
// @access  Private/Admin
exports.updatePermission = async (req, res) => {
  try {
    const { name, code, description, category, isActive } = req.body;

    let permission = await Permission.findById(req.params.id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    // Check if it's a system permission and prevent modification of critical fields
    if (permission.isSystem && (code !== permission.code)) {
      return res.status(400).json({
        success: false,
        message: 'Cannot modify system permission code'
      });
    }

    // Check if new code already exists (if code is being changed)
    if (code && code !== permission.code) {
      const existingPermission = await Permission.findOne({ code });
      if (existingPermission) {
        return res.status(400).json({
          success: false,
          message: 'Permission with this code already exists'
        });
      }
    }

    // Update permission
    permission = await Permission.findByIdAndUpdate(
      req.params.id,
      {
        name: name || permission.name,
        code: code || permission.code,
        description: description || permission.description,
        category: category || permission.category,
        isActive: isActive !== undefined ? isActive : permission.isActive
      },
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: permission
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Delete permission
// @route   DELETE /api/permissions/:id
// @access  Private/Admin
exports.deletePermission = async (req, res) => {
  try {
    const permission = await Permission.findById(req.params.id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    // Prevent deletion of system permissions
    if (permission.isSystem) {
      return res.status(400).json({
        success: false,
        message: 'Cannot delete system permission'
      });
    }

    // Check if any roles have this permission
    const rolesWithPermission = await Role.countDocuments({ permissions: req.params.id });
    if (rolesWithPermission > 0) {
      return res.status(400).json({
        success: false,
        message: `Cannot delete permission. ${rolesWithPermission} role(s) have this permission`
      });
    }

    await Permission.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'Permission deleted successfully'
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Toggle permission status
// @route   PATCH /api/permissions/:id/toggle-status
// @access  Private/Admin
exports.togglePermissionStatus = async (req, res) => {
  try {
    const permission = await Permission.findById(req.params.id);

    if (!permission) {
      return res.status(404).json({
        success: false,
        message: 'Permission not found'
      });
    }

    // Prevent toggling system permissions
    if (permission.isSystem) {
      return res.status(400).json({
        success: false,
        message: 'Cannot toggle system permission status'
      });
    }

    // Toggle status
    permission.isActive = !permission.isActive;
    await permission.save();

    res.status(200).json({
      success: true,
      data: permission,
      message: `Permission ${permission.isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get permission statistics
// @route   GET /api/permissions/stats
// @access  Private/Admin
exports.getPermissionStats = async (req, res) => {
  try {
    const totalPermissions = await Permission.countDocuments();
    const activePermissions = await Permission.countDocuments({ isActive: true });
    const systemPermissions = await Permission.countDocuments({ isSystem: true });
    const customPermissions = await Permission.countDocuments({ isSystem: false });

    // Get permission count by category
    const permissionsByCategory = await Permission.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get role count per permission
    const permissionRoleCounts = await Role.aggregate([
      { $unwind: '$permissions' },
      {
        $group: {
          _id: '$permissions',
          roleCount: { $sum: 1 }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalPermissions,
        activePermissions,
        systemPermissions,
        customPermissions,
        permissionsByCategory,
        permissionRoleCounts
      }
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};
