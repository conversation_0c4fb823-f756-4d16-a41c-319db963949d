import axios from 'axios';
import config from '../config';

const API_URL = `${config.API_URL}/roles`;

// Get auth token
const getAuthToken = () => {
  return localStorage.getItem('token');
};

// Set up axios interceptor for auth
const axiosInstance = axios.create();
axiosInstance.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Get all roles
export const getRoles = async () => {
  try {
    const response = await axiosInstance.get(API_URL);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch roles');
  }
};

// Get single role
export const getRole = async (id) => {
  try {
    const response = await axiosInstance.get(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch role');
  }
};

// Create new role
export const createRole = async (roleData) => {
  try {
    const response = await axiosInstance.post(API_URL, roleData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to create role');
  }
};

// Update role
export const updateRole = async (id, roleData) => {
  try {
    const response = await axiosInstance.put(`${API_URL}/${id}`, roleData);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to update role');
  }
};

// Delete role
export const deleteRole = async (id) => {
  try {
    const response = await axiosInstance.delete(`${API_URL}/${id}`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to delete role');
  }
};

// Get role statistics
export const getRoleStats = async () => {
  try {
    const response = await axiosInstance.get(`${API_URL}/stats`);
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch role statistics');
  }
};
