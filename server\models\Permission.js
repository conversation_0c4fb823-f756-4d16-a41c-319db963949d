const mongoose = require('mongoose');

const PermissionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a permission name'],
    trim: true,
    maxlength: [50, 'Permission name cannot be more than 50 characters']
  },
  code: {
    type: String,
    required: [true, 'Please add a permission code'],
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^[a-z_]+$/, 'Permission code must contain only lowercase letters and underscores']
  },
  description: {
    type: String,
    required: [true, 'Please add a permission description'],
    maxlength: [200, 'Description cannot be more than 200 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a permission category'],
    enum: ['Administration', 'File Operations', 'Analytics', 'Security'],
    default: 'File Operations'
  },
  isSystem: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
PermissionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for role count (will be populated when needed)
PermissionSchema.virtual('roleCount', {
  ref: 'Role',
  localField: '_id',
  foreignField: 'permissions',
  count: true
});

// Ensure virtual fields are serialized
PermissionSchema.set('toJSON', { virtuals: true });
PermissionSchema.set('toObject', { virtuals: true });

module.exports = mongoose.model('Permission', PermissionSchema);
